import { fetchMeetGenerate } from '@/services/meeting';
import { Toast } from '@douyinfe/semi-ui';
import { useEffect, useRef } from 'react';
import GenerateConfig from './components/GenerateConfig';
import GenerateTable from './components/GenerateTable';

import styles from './index.less';
const MeetingMinutesGeneration: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const tableRef = useRef<any>(null);

  useEffect(() => {
    if (containerRef.current) {
      const resizeObserver = new ResizeObserver((_) => {
        const windowHeight = window.innerHeight;
        const containerHeight = windowHeight - 140;
        if (containerRef.current) {
          containerRef.current.style.height = `${containerHeight}px`;
        }
      });
      resizeObserver.observe(containerRef.current);
      return () => {
        resizeObserver.disconnect();
      };
    }
  }, []);

  // 获取 GenerateConfig 传递的配置
  const handleGenerateConfig = async (v: any) => {
    try {
      const res = await fetchMeetGenerate(v);
      if (res.data) {
        Toast.success(res.data + '');
        tableRef?.current.startGenerate();
      }
    } catch (error) {
      console.log('fetchMeetGenerate=>', error);
    }
  };

  return (
    <div ref={containerRef} className={styles.meeting}>
      <GenerateConfig onGenerateConfig={handleGenerateConfig} />
      <div className="mt-8">
        <GenerateTable ref={tableRef} />
      </div>
    </div>
  );
};

export default MeetingMinutesGeneration;
