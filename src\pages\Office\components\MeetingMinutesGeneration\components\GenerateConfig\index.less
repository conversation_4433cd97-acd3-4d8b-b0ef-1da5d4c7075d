.meetingPage {
  width: 800px;
  height: 340px;
  border-radius: 12px;
  border: 1px solid #ebeef2;
  padding: 24px;
  display: flex;
  flex-direction: row;

  .meetingPageL {
    width: 390px;
    height: 100%;
    border: 1px dashed #005bf8;
    border-radius: 12px;
    margin-right: 24px;
    background: #f8faff;
    position: relative;

    .meetingPageLUp {
      width: 100%;
      height: 100%;

      :global {
        .semi-upload-add {
          width: 100%;
          height: 100%;
          background: none;
          border: none;
          border-radius: 16px !important;
        }
      }
    }

    .meetingPageLC {
      width: 100%;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: center;

      .meetingPageLCW {
        width: 62%;
        height: 160px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .meetingPageLCWI {
          width: 56px;
          height: 56px;
        }

        .meetingPageLCWC {
          font-size: 14px;
          font-weight: 500;
          margin-top: 24px;
          margin-bottom: 12px;

          .meetingPageLCWCL {
            color: #005bf8;
            margin-right: 5px;
          }

          .meetingPageLCWCR {
            color: #000;
          }
        }

        .meetingPageLCWT {
          font-size: 12px;
          color: #555;
          text-align: center;
          line-height: 24px;
        }
      }

      .meetingPageLCT {
        width: 100%;
        padding: 0 12px;
        display: flex;
        flex-direction: column;
        align-items: center;

        .meetingPageLCWI {
          width: 56px;
          height: 56px;
          margin-bottom: 6px;
        }

        .meetingPageLCWN {
          width: 80%;
          color: #555;
          font-size: 14px;
          margin-bottom: 6px;
          text-align: center;
        }

        .meetingPageLCWS {
          color: #999;
          font-size: 12px;
          margin-bottom: 16px;
        }

        .meetingPageLCTT {
          display: flex;
          align-items: center;
          border-bottom: 1px solid transparent;

          .meetingPageLCWST {
            width: 12px;
            height: 12px;
            margin-right: 6px;
            animation: rotate360 2s linear infinite;
          }

          .meetingPageLCWSTT {
            color: #005bf8;
            font-size: 12px;
          }
        }
      }

      .clearButton {
        position: absolute;
        right: 20px;
        top: 20px;
        cursor: pointer;
        pointer-events: auto;
      }
    }
  }

  .meetingPageR {
    flex: 1;
    height: 100%;
    position: relative;

    .meetingPageR_title {
      .meetingPageR_title_span {
        font-size: 14px;
        font-weight: 500;
        color: #000;
        margin-bottom: 12px;
      }

      .meetingPageR_title_div {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 24px;

        .meetingPageR_title_div_item {
          padding: 6px 12px;
          border-radius: 4px;
          border: 1px solid #ebeef2;
          font-size: 14px;
          color: #000;
          cursor: pointer;

          :global(.semi-select) {
            height: unset !important;
            background: transparent !important;

            &:hover {
              border-color: transparent !important;
            }

            &:focus {
              border-color: transparent !important;
            }

            &:active {
              border-color: transparent !important;
            }
          }

          :global(.semi-select-open) {
            border-color: transparent !important;
          }

          :global(.semi-select-focus) {
            border-color: transparent !important;
          }

          .meetingPageR_title_div_item_select :global(.semi-select-selection) {
            color: #005bf8 !important;
          }
        }
      }
    }

    .meetingPageR_btn {
      position: absolute;
      bottom: 0;
      right: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 80px;
      height: 40px;
      border-radius: 4px;
      font-size: 16px;
      color: #fff;
      background: #005bf8;
      cursor: pointer;
    }
  }
}

@keyframes rotate360 {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}
