import EmptyImage from '@/assets/chat/fallbackImage.svg';
import ReloadIcon from '@/assets/office/reload.svg';
import {
  HistoryItemType,
  deleteTask,
  fetchMeetRewrite,
  getHistory,
  meetTaskStop,
} from '@/services/meeting';
import { Input, InputGroup, Popconfirm, Table, Tooltip, Typography } from '@douyinfe/semi-ui';
import { forwardRef, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import { useNavigate } from 'umi';
import styles from './index.less';
interface GenerateTableRef {
  startGenerate: () => void;
}

const statusNameColor = {
  转写失败: '#EC4F4F',
  排队中: '#005BF8',
  转写中: '#005BF8',
  转写成功: '#000',
  任务取消: '#EC4F4F',
};

const GenerateTable = forwardRef<GenerateTableRef>((_, ref) => {
  const { Text } = Typography;
  let navigate = useNavigate();
  const [isRotating, setIsRotating] = useState(false);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(5);
  const [total, setTotal] = useState(0);
  const [dataSource, setDataSource] = useState<HistoryItemType[]>([]);
  const [searchName, setSearchName] = useState('');

  // 停止转写
  const handleStop = async (id: string) => {
    try {
      const res = await meetTaskStop({ id });
      if (res.data) {
        if (searchName) {
          setCurrentPage(1);
          getTableData(1, pageSize, searchName);
        } else {
          getTableData(currentPage, pageSize);
        }
      }
    } catch (error) {
      console.log('停止转写失败', error);
    }
  };
  // 重新转写
  const handleReWrite = async (id: string) => {
    try {
      const res = await fetchMeetRewrite({ id });
      if (res.data) {
        if (searchName) {
          setCurrentPage(1);
          getTableData(1, pageSize, searchName);
        } else {
          getTableData(currentPage, pageSize);
        }
      }
    } catch (error) {
      console.log('重新转写失败', error);
    }
  };
  // 删除
  const handleDelete = async (id: string) => {
    try {
      const res = await deleteTask({ id });
      if (res.data) {
        setTotal((prevTotal) => prevTotal - 1);
        adjustPageAfterDelete(currentPage, total - 1);
      }
    } catch (error) {
      console.log('删除失败', error);
    }
  };
  // 分页计算
  const adjustPageAfterDelete = (currentPage: number, newTotal: number) => {
    // 计算删除后的总页数
    const totalPagesAfterDelete = Math.ceil(newTotal / pageSize);

    // 如果当前页没有数据了，且不是第一页，则跳转到前一页
    if (totalPagesAfterDelete > 0 && currentPage > totalPagesAfterDelete) {
      const targetPage = totalPagesAfterDelete;
      setCurrentPage(targetPage);
      getTableData(targetPage, pageSize, searchName);
      return;
    }

    // 如果当前页是最后一页且没有数据了，跳转到前一页
    if (
      totalPagesAfterDelete === 0 ||
      (currentPage === totalPagesAfterDelete && newTotal % pageSize === 0 && newTotal > 0)
    ) {
      const targetPage = Math.max(1, totalPagesAfterDelete);
      setCurrentPage(targetPage);
      getTableData(targetPage, pageSize, searchName);
      return;
    }

    // 否则正常刷新当前页
    getTableData(currentPage, pageSize, searchName);
  };
  // 刷新
  const handleReloadClick = () => {
    setIsRotating(true);
    getTableData(currentPage, pageSize, searchName);
    setTimeout(() => {
      setIsRotating(false);
    }, 1000);
  };

  const handleChange = (currentPage: number, pageSize: number) => {
    setCurrentPage(currentPage);
    setPageSize(pageSize);
    // window.history.replaceState(
    //   { ...window.history.state, currentPage, pageSize },
    //   '',
    //   `?page=${currentPage}&size=${pageSize}&activeTab=meeting`,
    // );
    getTableData(currentPage, pageSize, searchName);
  };
  const getTableData = async (pageNo: number, pageSize: number, fileName = '') => {
    setLoading(true);
    try {
      const res = await getHistory({ pageNo, pageSize, fileName });
      if (res.data) {
        setDataSource(res.data.list);
        setTotal(res.data.total);
      }
    } catch (error) {
      console.log('getHistory=>', error);
    } finally {
      setLoading(false);
    }
  };
  //
  const columns = useMemo(
    () => [
      {
        title: '文件',
        dataIndex: 'fileName',
        width: 110,
        render: (text: string, record: any, index: number) => (
          <Text
            key={index}
            style={{
              display: 'block',
              color: record.statusName === '转写成功' ? '#005BF8' : '#555',
              borderBottom: record.statusName === '转写成功' ? '1px solid #005BF8' : 'none',
              cursor: record.statusName === '转写成功' ? 'pointer' : 'default',
            }}
            ellipsis={{
              rows: 1,
              showTooltip: true,
            }}
            onClick={() => {
              // 保存当前状态后再跳转
              window.history.replaceState(
                { ...window.history.state, currentPage, pageSize },
                '',
                `?page=${currentPage}&size=${pageSize}&searchName=${searchName}&activeTab=meeting`,
              );
              navigate(`/chat/office/meeting?id=${record.id}`, {
                state: {
                  id: record.id,
                  fileName: record.fileName,
                },
              });
            }}
          >
            {text}
          </Text>
        ),
      },
      {
        title: '类型',
        dataIndex: 'fileTypeName',
        width: 60,
      },
      {
        title: '时长',
        dataIndex: 'duration',
        width: 80,
        render: (text: string, record: any, index: number) => {
          return <div>{text || '--'}</div>;
        },
      },
      {
        title: '大小',
        dataIndex: 'fileSize',
        width: 90,
      },
      {
        title: '创建时间',
        dataIndex: 'createTime',
        width: 130,
        sorter: (a: any, b: any) => (a.updateTime - b.updateTime > 0 ? 1 : -1),
        // render: (value) => {
        //   return dateFns.format(new Date(value), 'yyyy-MM-dd');
        // },
      },
      {
        title: '状态',
        dataIndex: 'statusName',
        width: 80,
        render: (text: number, record: any, index: number) => (
          <div key={index} className="flex items-center">
            <span
              style={{
                marginRight: '4px',
                color: statusNameColor[record.statusName as keyof typeof statusNameColor],
              }}
            >
              {text}
            </span>
            {/* {record.status === 1 && <img src={SpinIcon} className={styles.TablespinIcon} />} */}
          </div>
        ),
      },
      {
        title: '操作',
        dataIndex: 'action',
        width: 120,
        render: (text: number, record: any, index: number) => (
          <div key={index}>
            {(record.statusName === '排队中' || record.statusName === '转写中') && (
              <Popconfirm
                title="确定要停止转写吗？"
                content="停止后，内容无法恢复。"
                onConfirm={() => handleStop(record.id)}
              >
                <span className={styles.spanColor}>停止转写</span>
              </Popconfirm>
            )}
            {(record.statusName === '转写成功' ||
              record.statusName === '转写失败' ||
              record.statusName === '任务取消') && (
              <Popconfirm
                title="确定要删除此条记录吗？"
                content="删除后，内容无法恢复。"
                onConfirm={() => handleDelete(record.id)}
              >
                <span style={{ marginRight: '16px' }} className={styles.spanColor}>
                  删除
                </span>
              </Popconfirm>
            )}
            {(record.statusName === '转写失败' || record.statusName === '任务取消') && (
              // <Popconfirm title="确定要重新转写吗？" content="重新转写后，原内容将被覆盖。">
              //   <span className={styles.spanColor}>重新转写</span>
              // </Popconfirm>
              <span className={styles.spanColor} onClick={() => handleReWrite(record.id)}>
                重新转写
              </span>
            )}
          </div>
        ),
      },
    ],
    [handleDelete],
  );
  //
  useImperativeHandle(ref, () => ({
    startGenerate: () => {
      setSearchName('');
      setCurrentPage(1);
      getTableData(1, pageSize);
    },
  }));

  const clearUrlParams = () => {
    const url = new URL(window.location.href);
    url.search = '';
    window.history.replaceState({}, '', url.toString());
  };

  const debouncedHandleChange = (value: string) => {
    setSearchName(value);
  };

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const savedPage = parseInt(urlParams.get('page') || '1', 10);
    const savedPageSize = parseInt(urlParams.get('size') || '5', 10);
    const savedSearchName = urlParams.get('searchName') || '';

    if (savedPage > 0) setCurrentPage(savedPage);
    if (savedPageSize > 0) setPageSize(savedPageSize);
    if (savedSearchName) setSearchName(savedSearchName);

    getTableData(savedPage, savedPageSize, savedSearchName);

    setTimeout(() => {
      clearUrlParams();
    }, 100);
  }, []);

  return (
    <div className={styles.generateTable}>
      <div className={styles.title}>
        <div className="flex items-center">
          <div className={styles.text}>最近记录</div>
          <InputGroup>
            <Input
              placeholder="请输入文件名称"
              style={{ width: 230, marginLeft: 20, fontSize: 14, borderRadius: 4 }}
              value={searchName}
              onChange={debouncedHandleChange}
              onClear={() => {
                setCurrentPage(1);
                getTableData(1, pageSize, '');
              }}
              onEnterPress={() => {
                setCurrentPage(1);
                getTableData(1, pageSize, searchName);
              }}
              showClear
            />
            {/* <Button
              icon={<SearchIcon />}
              onClick={() => {
                setCurrentPage(1);
                getTableData(1, pageSize, searchName);
              }}
            ></Button> */}
          </InputGroup>
        </div>
        <div className="flex items-center">
          <Tooltip content={'刷新'}>
            <img
              src={ReloadIcon}
              className={`${styles.icon} ${isRotating ? styles.rotate : ''}`}
              onClick={handleReloadClick}
            />
          </Tooltip>
          {/* <Tooltip content={'查询'}>
            <IconSearchStroked
              style={{ cursor: 'pointer' }}
              onClick={() => {
                setCurrentPage(1);
                getTableData(1, pageSize, searchName);
              }}
            />
          </Tooltip> */}
        </div>
      </div>
      <Table
        columns={columns}
        dataSource={dataSource}
        empty={
          <div className={styles.emptyMessageContainer}>
            <img className={styles.emptyImage} src={EmptyImage} />
            <div className={styles.emptyMessageText}>暂无历史记录</div>
          </div>
        }
        loading={loading}
        sticky
        pagination={{
          formatPageText: (e: any) => <span>共 {e.total} 项数据</span>,
          currentPage,
          pageSize,
          total,
          showSizeChanger: true,
          showQuickJumper: true,
          pageSizeOpts: [5, 10, 15, 20, 50],
          onChange: handleChange,
        }}
      />
    </div>
  );
});

export default GenerateTable;
